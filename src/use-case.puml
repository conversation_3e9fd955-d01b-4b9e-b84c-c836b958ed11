@startuml
skinparam defaultFontName "SimSun"
skinparam defaultFontSize 11
skinparam backgroundColor white
skinparam actorBackgroundColor #E1F5FE
skinparam usecaseBackgroundColor #F3E5F5
skinparam packageBackgroundColor #E8F5E8

title 聊天室系统整体用例图

left to right direction

actor "普通用户" as User
actor "群组创建者" as Creator

rectangle "聊天室系统" {

    package "用户管理" {
        usecase "用户注册" as UC01
        usecase "用户登录" as UC02
        usecase "用户登出" as UC03
        usecase "查看在线用户" as UC04
        usecase "更新用户状态" as UC05
    }

    package "聊天功能" {
        usecase "发送文本消息" as UC06
        usecase "接收消息" as UC07
        usecase "查看聊天历史" as UC08
        usecase "发送图片" as UC09
        usecase "语音聊天" as UC10
        usecase "录制语音" as UC11
        usecase "播放语音" as UC12
    }

    package "群组管理" {
        usecase "创建群组" as UC13
        usecase "加入群组" as UC14
        usecase "退出群组" as UC15
        usecase "群组聊天" as UC16
    }

    package "私聊功能" {
        usecase "发起私聊" as UC17
        usecase "私人聊天" as UC18
        usecase "查看私聊记录" as UC19
    }

    package "小组功能" {
        usecase "创建小组" as UC20
        usecase "邀请加入小组" as UC21
        usecase "接受小组邀请" as UC22
        usecase "拒绝小组邀请" as UC23
        usecase "小组聊天" as UC24
        usecase "离开小组" as UC25
        usecase "解散小组" as UC26
    }

    package "界面管理" {
        usecase "管理聊天窗口" as UC27
        usecase "切换聊天标签" as UC28
        usecase "最小化窗口" as UC29
        usecase "关闭聊天窗口" as UC30
    }
}

' 普通用户关联
User --> UC01
User --> UC02
User --> UC03
User --> UC04
User --> UC05
User --> UC06
User --> UC07
User --> UC08
User --> UC09
User --> UC10
User --> UC11
User --> UC12
User --> UC13
User --> UC14
User --> UC15
User --> UC16
User --> UC17
User --> UC18
User --> UC19
User --> UC20
User --> UC21
User --> UC22
User --> UC23
User --> UC24
User --> UC25
User --> UC27
User --> UC28
User --> UC29
User --> UC30

' 群组创建者关联（额外权限）
Creator --> UC26

' 包含关系
UC16 .> UC06 : include
UC16 .> UC07 : include
UC18 .> UC06 : include
UC18 .> UC07 : include
UC24 .> UC06 : include
UC24 .> UC07 : include
UC10 .> UC11 : include
UC10 .> UC12 : include
UC02 .> UC04 : include

' 扩展关系
UC09 .> UC06 : extend
UC11 .> UC06 : extend
UC05 .> UC02 : extend

@enduml