@startuml
title 用户动态列表活动图

start

:用户启动聊天客户端;

:连接服务器;

if (连接成功?) then (是)
  :初始化用户列表;
else (否)
  :显示连接失败;
  stop
endif

:请求获取在线用户列表;

:服务器返回用户列表;

:显示用户列表界面;

repeat
  :等待用户列表更新事件;
  
  if (事件类型?) then (新用户上线)
    :添加用户到列表;
    :显示在线状态;
    
  elseif (事件类型?) then (用户下线)
    :从列表移除用户;
    :更新显示;
    
  elseif (事件类型?) then (用户状态变化)
    :更新用户状态图标;
    
  else (用户信息更新)
    :刷新用户信息;
  endif
  
  :更新用户列表显示;
  
repeat while (客户端运行?) is (是)

:断开连接;
:清理资源;

stop

@enduml
