@startuml
title 用户列表更新顺序图

actor User
participant "客户端" as Client
participant "服务器" as Server
participant "UserManager" as UserManager
participant "其他客户端" as OtherClients

== 用户登录更新列表 ==
User -> Client : 启动客户端并登录
Client -> Server : 发送登录请求
Server -> UserManager : 调用 addUser 方法
UserManager -> Server : 返回添加结果
Server -> Client : 返回登录成功
Server -> OtherClients : 广播用户上线通知
OtherClients -> OtherClients : 更新用户列表显示
Client -> User : 显示登录成功界面

== 获取用户列表 ==
Client -> Server : 请求在线用户列表
Server -> UserManager : 调用 getOnlineUsers 方法
UserManager -> Server : 返回用户列表数据
Server -> Client : 发送用户列表
Client -> User : 显示用户列表

== 用户状态变化更新 ==
User -> Client : 用户操作导致状态变化
Client -> Server : 发送状态更新请求
Server -> UserManager : 调用 updateUserStatus 方法
UserManager -> Server : 返回更新结果
Server -> Client : 确认状态更新
Server -> OtherClients : 广播状态变化通知
OtherClients -> OtherClients : 更新用户状态显示
Client -> User : 显示状态更新成功

== 用户下线更新列表 ==
User -> Client : 用户退出登录
Client -> Server : 发送登出请求
Server -> UserManager : 调用 removeUser 方法
UserManager -> Server : 返回移除结果
Server -> OtherClients : 广播用户下线通知
OtherClients -> OtherClients : 从列表移除用户
Server -> Client : 确认登出成功
Client -> User : 显示退出成功

== 心跳检测更新 ==
loop 定时心跳检测
    Client -> Server : 发送心跳包
    Server -> UserManager : 更新用户活跃时间
    UserManager -> Server : 返回更新结果

    alt 心跳超时
        Server -> UserManager : 标记用户离线
        UserManager -> Server : 返回处理结果
        Server -> OtherClients : 广播用户离线通知
        OtherClients -> OtherClients : 移除超时用户
    end
end

@enduml