@startuml
title 聊天室系统Socket通信技术实现图

' 定义样式
skinparam ComponentBorderColor #2196F3
skinparam ComponentBackgroundColor #E3F2FD
skinparam ArrowColor #FF5722
skinparam NoteBackgroundColor #FFF3E0
skinparam NoteBorderColor #FFB74D
skinparam ArrowThickness 2

' 客户端A - 多连接示例
package "客户端A (ChatClient)" as ClientA {
  component "ChatUI\n(用户界面)" as ClientAUI
  component "ClientHandler\n(消息处理)" as ClientAHandler
  component "Socket连接池" as ClientASocketPool {
    component "Socket1\n(主连接)" as ClientASocket1
    component "Socket2\n(语音连接)" as ClientASocket2
  }
  component "线程管理器" as ClientAThreadManager {
    component "接收线程1" as ClientAReceiveThread1
    component "接收线程2" as ClientAReceiveThread2
  }
}

' 客户端B - 单连接示例
package "客户端B (ChatClient)" as ClientB {
  component "ChatUI\n(用户界面)" as ClientBUI
  component "ClientHandler\n(消息处理)" as ClientBHandler
  component "Socket连接" as ClientBSocket
  component "接收线程" as ClientBReceiveThread
}

' 服务端
package "服务端 (ChatServer)" as Server {
  component "ServerSocket\n(监听端口9999)" as ServerListener
  component "连接管理器\nConnectionManager" as ConnectionManager
  component "客户端处理线程池" as ServerThreadPool {
    component "ClientHandler1\n(处理客户端A)" as ServerHandler1
    component "ClientHandler2\n(处理客户端B)" as ServerHandler2
  }
  component "业务管理器集群" as BusinessManagers {
    component "UserManager\n(用户管理)" as UserManager
    component "MessageManager\n(消息管理)" as MessageManager
    component "GroupManager\n(群组管理)" as GroupManager
  }
}

' Socket连接建立过程
ClientASocket1 <--> ServerHandler1 : TCP连接\n(ObjectInputStream/ObjectOutputStream)
ClientASocket2 <--> ServerHandler1 : 语音数据连接
ClientBSocket <--> ServerHandler2 : TCP连接\n(ObjectInputStream/ObjectOutputStream)

' 服务端监听和连接分配
ServerListener --> ConnectionManager : 接受新连接
ConnectionManager --> ServerHandler1 : 分配ClientHandler线程
ConnectionManager --> ServerHandler2 : 分配ClientHandler线程

' 数据流向
ClientAUI --> ClientAHandler : 用户操作
ClientAHandler --> ClientASocket1 : 序列化Message对象
ClientBUI --> ClientBHandler : 用户操作
ClientBHandler --> ClientBSocket : 序列化Message对象

ServerHandler1 --> UserManager : 用户状态管理
ServerHandler1 --> MessageManager : 消息路由
ServerHandler1 --> GroupManager : 群组操作
ServerHandler2 --> UserManager : 用户状态管理
ServerHandler2 --> MessageManager : 消息路由

' 线程关系
ClientAReceiveThread1 --> ClientASocket1 : 监听消息
ClientAReceiveThread2 --> ClientASocket2 : 监听语音数据
ClientBReceiveThread --> ClientBSocket : 监听消息

' 技术实现说明
note top of ClientA
<b>客户端技术实现：</b>
• <b>Socket连接</b>：使用java.net.Socket建立TCP连接
• <b>对象序列化</b>：ObjectInputStream/ObjectOutputStream传输Message对象
• <b>多线程</b>：独立接收线程处理服务器消息
• <b>连接池</b>：支持多Socket连接（文本+语音）
• <b>异步处理</b>：UI线程与网络线程分离
end note

note top of Server
<b>服务端技术实现：</b>
• <b>ServerSocket监听</b>：端口9999监听客户端连接
• <b>线程池管理</b>：每个客户端分配独立ClientHandler线程
• <b>并发处理</b>：ConcurrentHashMap管理客户端连接
• <b>消息广播</b>：支持一对一、一对多消息分发
• <b>对象流通信</b>：序列化传输User、Message等业务对象
end note

note bottom of ConnectionManager
<b>连接管理核心逻辑：</b>
1. ServerSocket.accept()接受连接
2. 创建ClientHandler线程
3. 将Socket封装到ClientHandler
4. 启动线程处理客户端请求
5. 维护活跃连接Map<userId, ClientHandler>
end note

@enduml