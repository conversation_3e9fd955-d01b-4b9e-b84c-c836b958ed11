@startuml
title Socket 通信系统架构图

' 定义组件样式（可选，用于美化）
skinparam ComponentBorderColor #4488AA
skinparam ComponentBackgroundColor #E0F0FF
skinparam ArrowColor #FFD700
skinparam NoteBackgroundColor #FFFFCC
skinparam NoteBorderColor #CCCC99

' 客户端A相关组件
component "客户端A" as ClientA {
  component "管理线程的集合" as ClientAThreadSet
  component "线程" as ClientAThread1 {
    component "socket" as ClientASocket1
  }
  component "线程" as ClientAThread2 {
    component "socket" as ClientASocket2
  }
}

' 客户端B相关组件
component "客户端B" as ClientB {
  component "线程" as ClientBThread {
    component "socket" as ClientBSocket
  }
}

' 服务端相关组件
component "服务端" as Server {
  component "管理线程的集合" as ServerThreadSet
  component "线程" as ServerThread1 {
    component "socket" as ServerSocket1
  }
  component "线程" as ServerThread2 {
    component "socket" as ServerSocket2
  }
  component "监听9999" as ServerListener
}

' 客户端A与服务端交互：传递Message对象、User对象
ClientA -> Server : Message对象、User对象
Server -> ClientA : Message对象、User对象

' 客户端B与服务端交互：传递Message对象、User对象
ClientB -> Server : Message对象、User对象
Server -> ClientB : Message对象、User对象

' 服务端监听端口逻辑（示意箭头，体现监听与socket关联 ）
ServerListener --> ServerSocket1 : 客户端连接后分配socket
ServerListener --> ServerSocket2 : 客户端连接后分配socket

' 添加客户端说明备注
note left of ClientA
客户端逻辑：
1. 和服务端通信时，使用对象方式，可使用对象流来读写.
2. 当客户端连接到服务端后，也会得到socket
3. 启动一个线程，该线程持有socket.
4. 为了更好管理线程，也将该线程放入到集合
end note

' 添加服务端说明备注
note right of Server
服务端逻辑：
1. 当有客户端连接到服务器后，会得到一个socket
2. 启动一个线程，该线程持有该socket对象, 也就是说socket是该线程属性
3. 为了更好的管理线程，需要使用集合hm来管理
end note

@enduml