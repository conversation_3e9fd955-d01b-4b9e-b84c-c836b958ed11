@startuml
skinparam defaultFontName "SimSun"
skinparam defaultFontSize 10
skinparam backgroundColor white
skinparam sequenceParticipantBackgroundColor #E1F5FE
skinparam sequenceActorBackgroundColor #F3E5F5

title 聊天室系统整体顺序图

actor User as "用户"
participant Chat<PERSON> as "聊天界面"
participant ChatClient as "客户端"
participant ChatServer as "服务器"
participant UserManager as "用户管理器"
participant GroupManager as "群组管理器"
participant SubgroupManager as "小组管理器"
participant MessageManager as "消息管理器"
participant VoiceRecorder as "语音录制器"
participant UserListPanel as "用户列表"
participant OtherClients as "其他客户端"

== 系统启动和用户登录 ==
User -> ChatUI : 启动聊天客户端
ChatUI -> ChatUI : 显示登录对话框
User -> ChatUI : 输入用户ID和用户名
ChatUI -> ChatClient : login(userId, username)
ChatClient -> ChatServer : 发送LOGIN消息

ChatServer -> UserManager : 验证用户登录
alt 用户已在线
    UserManager -> ChatServer : 返回用户已在线错误
    ChatServer -> ChatClient : 发送登录失败消息
    ChatClient -> ChatUI : 显示错误信息
    ChatUI -> User : 提示用户已在线
else 登录成功
    UserManager -> UserManager : 添加用户到在线列表
    UserManager -> ChatServer : 返回登录成功
    ChatServer -> ChatClient : 发送登录成功消息
    ChatClient -> ChatUI : 更新登录状态
    ChatUI -> User : 显示主界面
end

== 获取系统信息 ==
ChatServer -> UserManager : 获取在线用户列表
UserManager -> ChatServer : 返回用户列表
ChatServer -> ChatClient : 广播USER_LIST消息
ChatClient -> UserListPanel : updateUserList(users)
UserListPanel -> User : 显示在线用户

ChatServer -> GroupManager : 获取群组列表
GroupManager -> ChatServer : 返回群组列表
ChatServer -> ChatClient : 发送GROUP_LIST消息
ChatClient -> ChatUI : updateGroupList(groups)
ChatUI -> User : 显示可用群组

== 群组聊天流程 ==
User -> ChatUI : 选择群组并加入
ChatUI -> ChatClient : joinGroup(groupId)
ChatClient -> ChatServer : 发送JOIN_GROUP消息
ChatServer -> GroupManager : 处理加入群组请求
GroupManager -> GroupManager : 验证群组权限
alt 加入成功
    GroupManager -> ChatServer : 返回加入成功
    ChatServer -> ChatClient : 发送加入成功消息
    ChatClient -> ChatUI : 打开群聊窗口
    ChatUI -> User : 显示群聊界面

    ChatServer -> OtherClients : 广播用户加入群组通知
    OtherClients -> OtherClients : 更新群组成员列表
else 加入失败
    GroupManager -> ChatServer : 返回加入失败
    ChatServer -> ChatClient : 发送错误消息
    ChatClient -> ChatUI : 显示错误信息
end

== 发送文本消息 ==
User -> ChatUI : 输入消息内容
ChatUI -> ChatClient : sendGroupMessage(groupId, content)
ChatClient -> ChatServer : 发送GROUP_MESSAGE
ChatServer -> MessageManager : 处理群组消息
MessageManager -> MessageManager : 保存消息到历史记录
MessageManager -> ChatServer : 获取群组成员列表

loop 广播给所有群成员
    ChatServer -> OtherClients : 发送GROUP_MESSAGE
    OtherClients -> OtherClients : 显示新消息
end

ChatServer -> ChatClient : 发送消息成功确认
ChatClient -> ChatUI : 更新发送状态
ChatUI -> User : 显示消息已发送

== 语音聊天流程 ==
User -> ChatUI : 点击语音按钮
ChatUI -> VoiceRecorder : startRecording()
VoiceRecorder -> VoiceRecorder : 初始化音频设备
VoiceRecorder -> ChatUI : 返回录制状态
ChatUI -> User : 显示录音中状态

User -> ChatUI : 说话并停止录音
ChatUI -> VoiceRecorder : stopRecording()
VoiceRecorder -> VoiceRecorder : 停止录制并获取音频数据
VoiceRecorder -> ChatUI : 返回音频数据

ChatUI -> ChatClient : sendVoiceMessage(groupId, audioData)
ChatClient -> ChatServer : 发送VOICE_MESSAGE
ChatServer -> MessageManager : 处理语音消息
MessageManager -> MessageManager : 保存语音数据

loop 广播语音消息给群成员
    ChatServer -> OtherClients : 发送VOICE_MESSAGE
    OtherClients -> OtherClients : 显示语音消息
    OtherClients -> OtherClients : 显示播放按钮
end

== 私聊功能 ==
User -> UserListPanel : 双击用户名
UserListPanel -> ChatUI : 触发私聊事件
ChatUI -> ChatClient : startPrivateChat(targetUserId)
ChatClient -> ChatServer : 发送PRIVATE_CHAT_REQUEST
ChatServer -> ChatServer : 验证目标用户在线状态
alt 目标用户在线
    ChatServer -> ChatClient : 发送私聊许可
    ChatClient -> ChatUI : 打开私聊窗口
    ChatUI -> User : 显示私聊界面
else 目标用户离线
    ChatServer -> ChatClient : 发送用户离线消息
    ChatClient -> ChatUI : 显示错误提示
    ChatUI -> User : 提示目标用户离线
end

== 小组功能流程 ==
User -> ChatUI : 创建小组
ChatUI -> ChatClient : createSubgroup(groupId, subgroupName, invitees)
ChatClient -> ChatServer : 发送CREATE_SUBGROUP消息
ChatServer -> SubgroupManager : 处理小组创建请求
SubgroupManager -> SubgroupManager : 创建小组对象
SubgroupManager -> ChatServer : 返回创建结果

loop 发送邀请给选中成员
    ChatServer -> OtherClients : 发送SUBGROUP_INVITE
    OtherClients -> OtherClients : 显示邀请对话框

    alt 接受邀请
        OtherClients -> ChatServer : 发送ACCEPT_INVITE
        ChatServer -> SubgroupManager : 添加成员到小组
        SubgroupManager -> ChatServer : 返回添加结果
        ChatServer -> ChatClient : 通知邀请接受
    else 拒绝邀请
        OtherClients -> ChatServer : 发送REJECT_INVITE
        ChatServer -> ChatClient : 通知邀请被拒绝
    end
end

== 连接异常处理 ==
loop 连接监听
    ChatClient -> ChatServer : 发送接收消息

    alt 连接正常
        ChatServer -> ChatClient : 正常消息处理
    else 连接异常
        ChatServer -> ChatServer : 检测到IOException
        ChatServer -> UserManager : 调用userLogout方法
        UserManager -> ChatServer : 返回处理结果
        ChatServer -> OtherClients : 广播用户离线通知
        OtherClients -> OtherClients : 更新用户列表

        ChatClient -> ChatUI : 显示连接中断提示
        ChatUI -> User : 询问是否重新连接

        alt 用户选择重连
            User -> ChatUI : 确认重连
            ChatUI -> ChatClient : 重新建立连接
        else 用户选择退出
            User -> ChatUI : 确认退出
            ChatUI -> ChatClient : 断开连接
            ChatClient -> ChatClient : 清理资源
        end
    end
end

== 用户登出 ==
User -> ChatUI : 点击退出按钮
ChatUI -> ChatClient : logout()
ChatClient -> ChatServer : 发送LOGOUT消息
ChatServer -> UserManager : 处理用户登出
UserManager -> UserManager : 从在线列表移除用户
UserManager -> ChatServer : 返回登出结果
ChatServer -> OtherClients : 广播用户离线通知
OtherClients -> OtherClients : 更新用户列表

ChatServer -> ChatClient : 发送登出成功确认
ChatClient -> ChatUI : 关闭主界面
ChatUI -> User : 显示登出成功

@enduml