@startuml
title User List Update Class Diagram

class User {
    -String userId
    -String username
    -boolean online
    +User(userId, username, online)
    +isOnline() : boolean
    +setOnline(online : boolean)
    +getUserId() : String
    +getUsername() : String
    +toString() : String
}

class Message {
    -MessageType type
    -List<User> userList
    -String senderId
    -long timestamp
    +setType(type : MessageType)
    +getType() : MessageType
    +setUserList(userList : List<User>)
    +getUserList() : List<User>
    +setSenderId(senderId : String)
    +setTimestamp(timestamp : long)
}

class ServerUserManager {
    -Map<String,User> users
    -Set<String> loggedUsers
    +addUser(user : User)
    +removeUser(userId : String)
    +getOnlineUsers() : List<User>
    +setUserStatus(userId : String, online : boolean)
    +getUser(userId : String) : User
    +isUserOnline(userId : String) : boolean
}

class ChatServer {
    -ServerUserManager userManager
    -List<ClientHandler> clients
    +userLogin(user : User)
    +userLogout(userId : String)
    +broadcastUserList()
    +getOnlineUsers() : List<User>
    +addClient(client : ClientHandler)
    +removeClient(client : ClientHandler)
}

class ClientHandler {
    -User user
    -ChatServer server
    -Socket socket
    -boolean running
    +sendMessage(message : Message)
    +getUser() : User
    +run()
    -handleLogin(message : Message)
    -handleLogout()
}

class ChatClient {
    -ChatUI ui
    -Socket socket
    -User currentUser
    -boolean connected
    +connect(host, port, userId, username) : boolean
    +sendMessage(message : Message)
    +disconnect()
    +isConnected() : boolean
    -processMessage(message : Message)
}

class ChatUI {
    -UserListPanel userListPanel
    -ChatClient client
    -String userId
    -String username
    +updateUserList(users : List<User>)
    +showError(message : String)
    +connectionLost()
    +getCurrentUserId() : String
}

class UserListPanel {
    -JList<User> userList
    -DefaultListModel<User> userListModel
    -ChatUI parentUI
    +updateUserList(users : List<User>)
    +getSelectedUser() : User
    +getUsers() : List<User>
}

' Relationships
ChatServer ||--|| ServerUserManager : uses
ChatServer ||--o{ ClientHandler : manages
ClientHandler ||--|| User : handles
ClientHandler ||--|| ChatServer : belongs to

ChatClient ||--|| ChatUI : communicates
ChatUI ||--|| UserListPanel : contains
ChatClient ||--|| User : current user

ServerUserManager ||--o{ User : manages
UserListPanel ||--o{ User : displays
Message ||--o{ User : contains

' Dependencies
ChatServer ..> Message : creates
ClientHandler ..> Message : processes
ChatClient ..> Message : sends/receives

@enduml